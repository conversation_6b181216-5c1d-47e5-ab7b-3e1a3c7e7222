import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-our-story',
  standalone: true,
  imports: [CommonModule],
  template: `
    <section class="py-20 px-6 sm:px-8 lg:px-12 min-h-screen">
      <div class="max-w-5xl mx-auto">
        <div class="text-center mb-20">
          <h1 class="font-playfair text-4xl sm:text-5xl lg:text-6xl text-romantic mb-6">
            Our Beautiful Story
          </h1>
          <p class="font-poppins text-xl text-gray-600 max-w-2xl mx-auto font-light">
            Every moment with you has been magical ✨
          </p>
        </div>

        <!-- Desktop Timeline -->
        <div class="hidden lg:block relative">
          <!-- Elegant Timeline Line -->
          <div class="absolute left-1/2 transform -translate-x-1/2 w-0.5 h-full bg-gradient-to-b from-pink-200 to-purple-200 rounded-full opacity-60"></div>

          <!-- Timeline Items -->
          <div class="space-y-20">
            @for (milestone of storyMilestones; track milestone.id; let i = $index) {
              <div class="flex items-center" [class.flex-row-reverse]="i % 2 === 1">
                <div class="w-1/2 px-12">
                  <div class="bg-white/80 backdrop-blur-sm rounded-2xl overflow-hidden shadow-lg card-hover border border-pink-100/50">
                    <!-- Beautiful Image -->
                    <div class="relative h-48 overflow-hidden">
                      <img
                        [src]="milestone.image"
                        [alt]="milestone.title"
                        class="w-full h-full object-cover hover:scale-105 transition-transform duration-700"
                      />
                      <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                      <div class="absolute top-4 left-4">
                        <span class="text-3xl animate-soft-pulse drop-shadow-lg">{{ milestone.emoji }}</span>
                      </div>
                    </div>

                    <!-- Content -->
                    <div class="p-8">
                      <h3 class="font-playfair text-2xl font-semibold text-gray-700 mb-4">{{ milestone.title }}</h3>
                      <p class="font-poppins text-gray-600 leading-relaxed mb-6 text-lg font-light">{{ milestone.description }}</p>
                      <span class="inline-block text-sm font-medium text-pink-600 bg-pink-50 px-4 py-2 rounded-full border border-pink-100">
                        {{ milestone.date }}
                      </span>
                    </div>
                  </div>
                </div>

                <!-- Elegant Timeline Dot -->
                <div class="w-6 h-6 bg-gradient-to-r from-pink-300 to-purple-300 rounded-full border-3 border-white shadow-md z-10 hover:scale-125 transition-transform duration-500"></div>

                <div class="w-1/2"></div>
              </div>
            }
          </div>
        </div>

        <!-- Mobile Timeline -->
        <div class="lg:hidden">
          <div class="space-y-12">
            @for (milestone of storyMilestones; track milestone.id) {
              <div class="relative pl-10">
                <!-- Mobile Timeline Line -->
                <div class="absolute left-4 top-0 bottom-0 w-0.5 bg-gradient-to-b from-pink-200 to-purple-200 opacity-60"></div>
                
                <!-- Mobile Timeline Dot -->
                <div class="absolute left-1 top-8 w-6 h-6 bg-gradient-to-r from-pink-300 to-purple-300 rounded-full border-2 border-white shadow-md"></div>
                
                <!-- Mobile Content Card -->
                <div class="bg-white/80 backdrop-blur-sm rounded-xl overflow-hidden shadow-lg card-hover border border-pink-100/50 ml-6">
                  <!-- Mobile Image -->
                  <div class="relative h-32 overflow-hidden">
                    <img
                      [src]="milestone.image"
                      [alt]="milestone.title"
                      class="w-full h-full object-cover"
                    />
                    <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                    <div class="absolute top-2 left-2">
                      <span class="text-2xl animate-soft-pulse drop-shadow-lg">{{ milestone.emoji }}</span>
                    </div>
                  </div>

                  <!-- Mobile Content -->
                  <div class="p-6">
                    <h3 class="font-playfair text-xl font-semibold text-gray-700 mb-3">{{ milestone.title }}</h3>
                    <p class="font-poppins text-gray-600 leading-relaxed mb-4 font-light">{{ milestone.description }}</p>
                    <span class="inline-block text-sm font-medium text-pink-600 bg-pink-50 px-3 py-2 rounded-full border border-pink-100">
                      {{ milestone.date }}
                    </span>
                  </div>
                </div>
              </div>
            }
          </div>
        </div>
      </div>
    </section>
  `,
  styles: []
})
export class OurStoryComponent {
  storyMilestones = [
    {
      id: 1,
      emoji: '👀',
      title: 'First Glance',
      description: 'The moment of our first glance and the world seemed to pause. I knew something special was about to begin.',
      date: 'Day 1',
      image: 'assets/images/first-conversation.jpg'
    },
    {
      id: 2,
      emoji: '💬',
      title: 'First Conversation',
      description: 'Hours flew by like minutes as we talked about everything and nothing. Your laugh became my favorite sound.',
      date: 'Day 3',
      image: 'assets/images/first-conversationn.jpg'
    },
    {
      id: 3,
      emoji: '📱',
      title: 'First Call',
      description: 'Staying in school until 6 PM just to hear your voice. Distance meant nothing when we were talking.',
      date: 'Day 7',
      image: 'assets/images/first-calll.PNG'
    },
    {
      id: 4,
      emoji: '💕',
      title: 'First "I Love You"',
      description: 'Three words that changed everything. The moment I knew my heart belonged to you completely.',
      date: 'Day 15',
      image: 'https://images.unsplash.com/photo-1518568814500-bf0f8d125f46?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80'
    },
    {
      id: 5,
      emoji: '🎉',
      title: 'Officially Together',
      description: 'The day we decided to write our love story together. Best decision I ever made.',
      date: 'Day 20',
      image: 'assets/images/official.jpg'
    }
  ];
}
