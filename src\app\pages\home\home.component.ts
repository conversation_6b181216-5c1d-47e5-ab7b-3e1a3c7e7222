import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [CommonModule, RouterLink],
  template: `
    <!-- Hero Section -->
    <section class="min-h-screen flex items-center justify-center relative overflow-hidden">
      <!-- Beautiful Background Image -->
      <div class="absolute inset-0">
        <img
          src="https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2025&q=80"
          alt="Romantic couple silhouette at sunset"
          class="w-full h-full object-cover opacity-15"
        />
        <div class="absolute inset-0 bg-gradient-to-br from-pink-50/90 to-purple-50/90"></div>
        <div class="absolute top-1/3 left-1/4 w-96 h-96 bg-pink-100 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-gentle-glow"></div>
        <div class="absolute bottom-1/3 right-1/4 w-80 h-80 bg-purple-100 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-gentle-glow animation-delay-2000"></div>
      </div>

      <!-- Floating Hearts -->
      <div class="absolute inset-0 pointer-events-none overflow-hidden">
        @for (heart of floatingHearts; track heart.id) {
          <div
            class="absolute text-pink-200 opacity-30 animate-gentle-float"
            [style.left.%]="heart.left"
            [style.top.%]="heart.top"
            [style.animation-delay]="heart.delay + 's'"
            [style.font-size.px]="heart.size">
            💖
          </div>
        }
      </div>

      <!-- Main Content -->
      <div class="relative z-10 text-center px-6 sm:px-8 lg:px-12 max-w-5xl mx-auto">
        <!-- Elegant Glass Card -->
        <div class="glass-effect rounded-3xl p-12 sm:p-16 lg:p-20 shadow-xl border border-white/20">
          <div class="mb-8">
            <span class="text-4xl sm:text-5xl animate-soft-pulse">💖</span>
          </div>
          
          <h1 class="font-dancing text-5xl sm:text-7xl lg:text-8xl text-romantic mb-6 animate-soft-pulse leading-tight">
            Happy 1st Monthsary
          </h1>
          
          <h2 class="font-playfair text-2xl sm:text-3xl lg:text-4xl text-gray-600 mb-8 italic font-medium">
            My Tangi 
          </h2>
          
          <p class="font-poppins text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed mb-12 font-light">
            Welcome to our digital love story, where every moment is treasured
            and every memory is painted with pure affection
          </p>
          
          <!-- Navigation Buttons -->
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a routerLink="/our-story" 
               class="bg-gradient-to-r from-pink-300 to-purple-300 text-gray-700 px-10 py-4 rounded-full font-poppins font-medium text-lg hover:shadow-xl transform hover:scale-105 transition-all duration-500 hover:from-pink-400 hover:to-purple-400 hover:text-white">
              Our Story ✨
            </a>
            <a routerLink="/love-notes" 
               class="bg-white/50 backdrop-blur-sm text-gray-700 px-10 py-4 rounded-full font-poppins font-medium text-lg hover:shadow-xl transform hover:scale-105 transition-all duration-500 border border-pink-200 hover:bg-pink-100">
              Love Notes 💕
            </a>
          </div>
        </div>
      </div>

      <!-- Minimal Sparkle Effects -->
      <div class="absolute top-20 left-20 text-pink-200 text-2xl animate-gentle-glow">✨</div>
      <div class="absolute bottom-20 right-20 text-purple-200 text-2xl animate-gentle-glow animation-delay-2000">💫</div>
    </section>

    <!-- Quick Preview Section -->
    <section class="py-20 px-6 sm:px-8 lg:px-12">
      <div class="max-w-6xl mx-auto">
        <div class="text-center mb-16">
          <h2 class="font-playfair text-4xl sm:text-5xl text-romantic mb-6">
            Our Journey Together
          </h2>
          <p class="font-poppins text-xl text-gray-600 max-w-2xl mx-auto font-light">
            Every day with you is a new chapter in our beautiful love story
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <!-- Story Preview -->
          <div class="glass-effect rounded-2xl overflow-hidden card-hover border border-white/20 group">
            <div class="relative h-32 overflow-hidden">
              <img
                src="https://images.unsplash.com/photo-1516589178581-6cd7833ae3b2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80"
                alt="Our Story"
                class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
              <div class="absolute top-2 left-2">
                <div class="text-3xl animate-soft-pulse drop-shadow-lg">📖</div>
              </div>
            </div>
            <div class="p-6 text-center">
              <h3 class="font-playfair text-2xl font-semibold text-gray-700 mb-4">Our Story</h3>
              <p class="font-poppins text-gray-600 mb-6 font-light">From our first glance to this beautiful moment</p>
              <a routerLink="/our-story" class="text-romantic font-medium hover:underline">Read More →</a>
            </div>
          </div>

          <!-- Love Notes Preview -->
          <div class="glass-effect rounded-2xl overflow-hidden card-hover border border-white/20 group">
            <div class="relative h-32 overflow-hidden">
              <img
                src="https://images.unsplash.com/photo-1518568814500-bf0f8d125f46?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80"
                alt="Love Notes"
                class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
              <div class="absolute top-2 left-2">
                <div class="text-3xl animate-soft-pulse drop-shadow-lg">💌</div>
              </div>
            </div>
            <div class="p-6 text-center">
              <h3 class="font-playfair text-2xl font-semibold text-gray-700 mb-4">Love Notes</h3>
              <p class="font-poppins text-gray-600 mb-6 font-light">All the things I love about you</p>
              <a routerLink="/love-notes" class="text-romantic font-medium hover:underline">Discover →</a>
            </div>
          </div>

          <!-- Memories Preview -->
          <div class="glass-effect rounded-2xl overflow-hidden card-hover border border-white/20 group">
            <div class="relative h-32 overflow-hidden">
              <img
                src="https://images.unsplash.com/photo-1529333166437-7750a6dd5a70?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80"
                alt="Memories"
                class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
              <div class="absolute top-2 left-2">
                <div class="text-3xl animate-soft-pulse drop-shadow-lg">📸</div>
              </div>
            </div>
            <div class="p-6 text-center">
              <h3 class="font-playfair text-2xl font-semibold text-gray-700 mb-4">Memories</h3>
              <p class="font-poppins text-gray-600 mb-6 font-light">Our precious moments together</p>
              <a routerLink="/memories" class="text-romantic font-medium hover:underline">Explore →</a>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Daily Conyo Love Messages Section -->
    <section class="py-20 px-6 sm:px-8 lg:px-12 bg-gradient-to-br from-pink-25 to-purple-25">
      <div class="max-w-6xl mx-auto">
        <div class="text-center mb-16">
          <h2 class="font-playfair text-4xl sm:text-5xl text-romantic mb-6">
            Daily Love Messages 💕
          </h2>
          <p class="font-poppins text-xl text-gray-600 max-w-2xl mx-auto font-light">
            Special conyo messages that unlock each day of our journey together
          </p>
          <div class="mt-4 text-sm text-gray-500">
            Today is {{ getCurrentDate() }} • {{ getUnlockedCount() }}/{{ dailyMessages.length }} messages unlocked
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          @for (message of dailyMessages; track message.day) {
            <div class="relative">
              <!-- Unlocked Message -->
              <div *ngIf="isMessageUnlocked(message.day)"
                   class="glass-effect rounded-2xl p-6 border border-white/20 card-hover transform transition-all duration-500 hover:scale-105">
                <div class="flex items-center justify-between mb-4">
                  <div class="flex items-center space-x-2">
                    <span class="text-2xl">{{ message.emoji }}</span>
                    <span class="font-poppins font-semibold text-gray-700">Day {{ message.day }}</span>
                  </div>
                  <div class="text-green-500 text-xl">🔓</div>
                </div>
                <div class="text-center">
                  <h3 class="font-dancing text-2xl text-romantic mb-3">{{ message.title }}</h3>
                  <p class="font-poppins text-gray-600 leading-relaxed italic">
                    "{{ message.message }}"
                  </p>
                  <div class="mt-4 text-xs text-gray-500">
                    Unlocked on {{ message.unlockDate }}
                  </div>
                </div>
              </div>

              <!-- Locked Message -->
              <div *ngIf="!isMessageUnlocked(message.day)"
                   class="glass-effect rounded-2xl p-6 border border-gray-200/50 opacity-60 relative overflow-hidden">
                <div class="absolute inset-0 bg-gradient-to-br from-gray-100/50 to-gray-200/50 backdrop-blur-sm"></div>
                <div class="relative z-10 text-center">
                  <div class="flex items-center justify-center mb-4">
                    <div class="text-4xl text-gray-400">🔒</div>
                  </div>
                  <h3 class="font-dancing text-xl text-gray-400 mb-2">Day {{ message.day }}</h3>
                  <p class="font-poppins text-gray-400 text-sm">
                    Unlocks on {{ message.unlockDate }}
                  </p>
                  <div class="mt-3 text-xs text-gray-400">
                    {{ getDaysUntilUnlock(message.day) }} days to go...
                  </div>
                </div>
              </div>
            </div>
          }
        </div>

        <!-- Progress Bar -->
        <div class="mt-12 text-center">
          <div class="max-w-md mx-auto">
            <div class="flex justify-between text-sm text-gray-600 mb-2">
              <span>Progress</span>
              <span>{{ getUnlockedCount() }}/{{ dailyMessages.length }}</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
              <div class="bg-gradient-to-r from-pink-400 to-purple-400 h-full rounded-full transition-all duration-1000"
                   [style.width.%]="getProgressPercentage()">
              </div>
            </div>
            <p class="mt-3 text-sm text-gray-500">
              {{ getProgressMessage() }}
            </p>
          </div>
        </div>
      </div>
    </section>
  `,
  styles: []
})
export class HomeComponent implements OnInit {
  floatingHearts = [
    { id: 1, left: 10, top: 20, size: 20, delay: 0 },
    { id: 2, left: 80, top: 10, size: 25, delay: 1 },
    { id: 3, left: 60, top: 70, size: 18, delay: 2 },
    { id: 4, left: 30, top: 50, size: 22, delay: 0.5 }
  ];

  // Starting date of your relationship - July 21, 2025 (your actual monthsary date)
  relationshipStartDate = new Date('2025-07-21'); // July 21, 2025

  dailyMessages = [
    {
      day: 1,
      emoji: '💕',
      title: 'Day One Magic',
      message: 'Another beautiful day of loving you my tangi! 💕',
      unlockDate: this.getUnlockDate(1)
    },
    {
      day: 2,
      emoji: '✨',
      title: 'Getting Kilig',
      message: 'You still give me butterflies pa rin, love! Grabe naman! ✨',
      unlockDate: this.getUnlockDate(2)
    },
    {
      day: 3,
      emoji: '🌟',
      title: 'So Smitten',
      message: 'I\'m so smitten with you, babe! You\'re perfect! 🌟',
      unlockDate: this.getUnlockDate(3)
    },
    {
      day: 4,
      emoji: '💖',
      title: 'Heart Eyes',
      message: 'You\'re so ganda talaga, my heart can\'t even! 💖',
      unlockDate: this.getUnlockDate(4)
    },
    {
      day: 5,
      emoji: '🥰',
      title: 'Sweet Vibes',
      message: 'You\'re the sweetest, most amazing girl ever! 🥰',
      unlockDate: this.getUnlockDate(5)
    },
    {
      day: 6,
      emoji: '💝',
      title: 'Grateful Heart',
      message: 'So grateful for you, mahal! You\'re amazing! 💝',
      unlockDate: this.getUnlockDate(6)
    },
    {
      day: 7,
      emoji: '🌈',
      title: 'One Week Vibes',
      message: 'One week na! You bring colors to my life! 🌈',
      unlockDate: this.getUnlockDate(7)
    },
    {
      day: 8,
      emoji: '💫',
      title: 'Dream Girl',
      message: 'You\'re my dream girl come true, love! So lucky! 💫',
      unlockDate: this.getUnlockDate(8)
    },
    {
      day: 9,
      emoji: '🦋',
      title: 'Butterfly Feels',
      message: 'You still give me butterflies, tangi! Kilig! 🦋',
      unlockDate: this.getUnlockDate(9)
    },
    {
      day: 10,
      emoji: '💐',
      title: 'Perfect Match',
      message: 'We\'re so perfect together, babe! Love you! 💐',
      unlockDate: this.getUnlockDate(10)
    },
    {
      day: 11,
      emoji: '🌸',
      title: 'Growing Love',
      message: 'My love grows stronger each day, tangi! 🌸',
      unlockDate: this.getUnlockDate(11)
    },
    {
      day: 12,
      emoji: '💞',
      title: 'So Blessed',
      message: 'So blessed to have you in my life, love! 💞',
      unlockDate: this.getUnlockDate(12)
    },
    {
      day: 13,
      emoji: '🎀',
      title: 'Lucky Me',
      message: 'Lucky 13! You\'re my good luck charm, babe! 🎀',
      unlockDate: this.getUnlockDate(13)
    },
    {
      day: 14,
      emoji: '💗',
      title: 'Two Weeks Strong',
      message: 'Two weeks na! Still amazed by you daily! 💗',
      unlockDate: this.getUnlockDate(14)
    },
    {
      day: 15,
      emoji: '🌺',
      title: 'Halfway There',
      message: 'Halfway to one month! Time flies with you! 🌺',
      unlockDate: this.getUnlockDate(15)
    },
    {
      day: 16,
      emoji: '💘',
      title: 'Sweet Sixteen',
      message: 'Sweet 16 days! You make my world brighter! 💘',
      unlockDate: this.getUnlockDate(16)
    },
    {
      day: 17,
      emoji: '🌻',
      title: 'My Sunshine',
      message: 'You\'re my sunshine, love! My happy place! 🌻',
      unlockDate: this.getUnlockDate(17)
    },
    {
      day: 18,
      emoji: '💓',
      title: 'Heartbeat',
      message: 'You make my heart beat faster, tangi! 💓',
      unlockDate: this.getUnlockDate(18)
    },
    {
      day: 19,
      emoji: '🎈',
      title: 'Cloud Nine',
      message: 'Floating on cloud nine because of you! 🎈',
      unlockDate: this.getUnlockDate(19)
    },
    {
      day: 20,
      emoji: '💎',
      title: 'Precious Gem',
      message: 'You\'re my precious gem, so priceless! 💎',
      unlockDate: this.getUnlockDate(20)
    },
    {
      day: 21,
      emoji: '🌙',
      title: 'Three Weeks',
      message: 'Three weeks na! Love you to the moon! 🌙',
      unlockDate: this.getUnlockDate(21)
    },
    {
      day: 22,
      emoji: '🦄',
      title: 'My Unicorn',
      message: 'You\'re my magical unicorn, so perfect! 🦄',
      unlockDate: this.getUnlockDate(22)
    },
    {
      day: 23,
      emoji: '🌷',
      title: 'Blooming Love',
      message: 'Our love keeps blooming, getting prettier! 🌷',
      unlockDate: this.getUnlockDate(23)
    },
    {
      day: 24,
      emoji: '⭐',
      title: 'My Star',
      message: 'You\'re my shining star, my inspiration! ⭐',
      unlockDate: this.getUnlockDate(24)
    },
    {
      day: 25,
      emoji: '🎁',
      title: 'Heaven Sent',
      message: 'You\'re a gift from heaven, my love! 🎁',
      unlockDate: this.getUnlockDate(25)
    },
    {
      day: 26,
      emoji: '🌊',
      title: 'Ocean Deep',
      message: 'My love is ocean deep, endless for you! 🌊',
      unlockDate: this.getUnlockDate(26)
    },
    {
      day: 27,
      emoji: '🔥',
      title: 'Burning Love',
      message: 'My love burns brighter each day, tangi! 🔥',
      unlockDate: this.getUnlockDate(27)
    },
    {
      day: 28,
      emoji: '🎪',
      title: 'Joy Circus',
      message: 'Life with you is pure joy and wonder! 🎪',
      unlockDate: this.getUnlockDate(28)
    },
    {
      day: 29,
      emoji: '🎭',
      title: 'Almost There',
      message: 'Almost one month! You\'re genuinely amazing! 🎭',
      unlockDate: this.getUnlockDate(29)
    },
    {
      day: 30,
      emoji: '🎉',
      title: 'ONE MONTH!',
      message: 'WE DID IT! One month of pure love and happiness! Forever to go, my tangi! 🎉💕',
      unlockDate: this.getUnlockDate(30)
    }
  ];

  ngOnInit() {
    // Component initialization
  }

  getUnlockDate(day: number): string {
    const unlockDate = new Date(this.relationshipStartDate);
    unlockDate.setDate(unlockDate.getDate() + (day - 1));
    return unlockDate.toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });
  }

  isMessageUnlocked(day: number): boolean {
    const today = new Date();
    const unlockDate = new Date(this.relationshipStartDate);
    unlockDate.setDate(unlockDate.getDate() + (day - 1));
    return today >= unlockDate;
  }

  getDaysUntilUnlock(day: number): number {
    const today = new Date();
    const unlockDate = new Date(this.relationshipStartDate);
    unlockDate.setDate(unlockDate.getDate() + (day - 1));
    const diffTime = unlockDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  }

  getCurrentDate(): string {
    return new Date().toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });
  }

  getUnlockedCount(): number {
    return this.dailyMessages.filter(message => this.isMessageUnlocked(message.day)).length;
  }

  getProgressPercentage(): number {
    return (this.getUnlockedCount() / this.dailyMessages.length) * 100;
  }

  getProgressMessage(): string {
    const unlockedCount = this.getUnlockedCount();

    if (unlockedCount === 0) {
      return "Your love journey is just beginning! 💕";
    } else if (unlockedCount < 7) {
      return "First week vibes! Getting to know each other 🥰";
    } else if (unlockedCount < 14) {
      return "Two weeks strong! The feelings are growing 💖";
    } else if (unlockedCount < 21) {
      return "Three weeks in! This is getting serious 😍";
    } else if (unlockedCount < 30) {
      return "Almost one month! The love is real 💞";
    } else {
      return "One month milestone achieved! Forever to go! 🎉";
    }
  }
}
