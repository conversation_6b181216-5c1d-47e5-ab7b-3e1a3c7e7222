import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-memories',
  standalone: true,
  imports: [CommonModule],
  template: `
    <section class="py-20 px-6 sm:px-8 lg:px-12 min-h-screen relative overflow-hidden">
      <!-- Beautiful Background -->
      <div class="absolute inset-0 z-0">
        <img
          src="https://images.unsplash.com/photo-1529333166437-7750a6dd5a70?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2025&q=80"
          alt="Romantic memories background"
          class="w-full h-full object-cover opacity-8"
        />
        <div class="absolute inset-0 bg-gradient-to-br from-pink-50/95 to-purple-50/95"></div>
      </div>

      <div class="max-w-6xl mx-auto relative z-10">
        <div class="text-center mb-20">
          <h1 class="font-playfair text-4xl sm:text-5xl lg:text-6xl text-romantic mb-6">
            Our Precious Memories
          </h1>
          <p class="font-poppins text-xl text-gray-600 max-w-2xl mx-auto font-light">
            Every moment we've shared is a treasure in my heart 📸
          </p>
        </div>

        <!-- Photo Gallery -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          @for (photo of photoGallery; track photo.id) {
            <div class="glass-effect rounded-2xl overflow-hidden card-hover border border-white/20 group">
              <!-- Beautiful Image -->
              <div class="relative h-48 overflow-hidden">
                <img
                  [src]="photo.image"
                  [alt]="photo.caption"
                  class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                />
                <div class="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent"></div>
                <div class="absolute top-4 left-4">
                  <span class="text-3xl animate-soft-pulse drop-shadow-lg">{{ photo.emoji }}</span>
                </div>
              </div>

              <!-- Content -->
              <div class="p-6 text-center">
                <h3 class="font-playfair text-xl font-semibold text-gray-700 mb-3">{{ photo.caption }}</h3>
                <p class="font-poppins text-gray-600 text-sm font-light">{{ photo.description }}</p>
              </div>
            </div>
          }
        </div>

    

        <!-- Promises Section -->
        <div class="text-center mb-16">
          <h2 class="font-playfair text-3xl sm:text-4xl text-romantic mb-6">
            My Promises to You
          </h2>
          <p class="font-poppins text-lg text-gray-600 max-w-2xl mx-auto font-light mb-12">
            These are the promises I make to you, today and always 💕
          </p>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          @for (promise of promises; track promise.id) {
            <div class="glass-effect rounded-2xl p-8 text-center card-hover border border-white/20">
              <div class="text-4xl mb-4 animate-soft-pulse">💝</div>
              <h3 class="font-playfair text-xl font-semibold text-gray-700 mb-4">{{ promise.title }}</h3>
              <p class="font-poppins text-gray-600 leading-relaxed font-light">{{ promise.text }}</p>
            </div>
          }
        </div>
      </div>
    </section>
  `,
  styles: []
})
export class MemoriesComponent {
  photoGallery = [
    {
      id: 1,
      emoji: '📸',
      caption: 'Our first selfie together',
      description: 'The moment we decided to capture our happiness',
      image: 'assets/images/official.jpg'
    },
    {
      id: 2,
      emoji: '🌅',
      caption: 'Watching the sunrise',
      description: 'Cause you are my sunrise :))',
      image: 'assets/images/sunrise.jpg'
    },
    {
      id: 3,
      emoji: '💕',
      caption: 'Our first goodmorning text',
      description: 'Starting the day with your sweet messages',
      image: 'assets/images/goodmorning.jpg'
    },
    {
      id: 4,
      emoji: '🎮',
      caption: 'Gaming together',
      description: 'Time flies when we\'re having fun',
      image: '/assets/images/gaming.jpg'
    },
    {
      id: 5,
      emoji: '💌',
      caption: 'The love letter that started it all',
      description: 'Words that touched my heart forever',
      image: 'assets/images/love-letter.png'
    },
    {
      id: 6,
      emoji: '🌙',
      caption: 'Good night texts every single day',
      description: 'Never missing a chance to say sweet dreams',
      image: 'assets/images/goodnight.jpg'
    }
  ];

  memoryJar = Array.from({ length: 30 }, (_, i) => ({
    id: i + 1,
    day: i + 1,
    unlocked: i < 10, // First 10 days unlocked for demo
    showMessage: false,
    message: `Day ${i + 1}: Another beautiful day of loving you! 💖`
  }));

  promises = [
    {
      id: 1,
      title: 'Always Listen',
      text: 'I promise to always listen to you with my heart, not just my ears.'
    },
    {
      id: 2,
      title: 'Support Your Dreams',
      text: 'I will always be your biggest cheerleader and support your goals.'
    },
    {
      id: 3,
      title: 'Make You Laugh',
      text: 'I promise to find new ways to make you smile every single day.'
    },
    {
      id: 4,
      title: 'Be Patient',
      text: 'I will be patient with you, especially when you are not patient with yourself.'
    },
    {
      id: 5,
      title: 'Love You More',
      text: 'I promise to love you more today than yesterday, but less than tomorrow.'
    },
    {
      id: 6,
      title: 'Never Stop Trying',
      text: 'I will never stop trying to be the best boyfriend you deserve.'
    }
  ];

  toggleMemory(dayId: number) {
    const day = this.memoryJar.find(d => d.id === dayId);
    if (day && day.unlocked) {
      day.showMessage = !day.showMessage;
      // Hide other messages
      this.memoryJar.forEach(d => {
        if (d.id !== dayId) d.showMessage = false;
      });
    }
  }
}
