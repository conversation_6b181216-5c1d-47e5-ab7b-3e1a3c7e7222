import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-love-notes',
  standalone: true,
  imports: [CommonModule],
  template: `
    <section class="py-20 px-6 sm:px-8 lg:px-12 min-h-screen relative overflow-hidden">
      <!-- Beautiful Background -->
      <div class="absolute inset-0 z-0">
        <img
          src="https://images.unsplash.com/photo-1518568814500-bf0f8d125f46?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2025&q=80"
          alt="Romantic heart background"
          class="w-full h-full object-cover opacity-10"
        />
        <div class="absolute inset-0 bg-gradient-to-br from-pink-50/95 to-purple-50/95"></div>
      </div>

      <div class="max-w-6xl mx-auto relative z-10">
        <div class="text-center mb-20">
          <h1 class="font-playfair text-4xl sm:text-5xl lg:text-6xl text-romantic mb-6">
            What I Love About You
          </h1>
          <p class="font-poppins text-xl text-gray-600 max-w-2xl mx-auto font-light">
            Every little thing about you makes my heart skip a beat 💕
          </p>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10">
          @for (loveNote of loveNotes; track loveNote.id) {
            <div class="flip-card h-80">
              <div class="flip-card-inner">
                <!-- Front of card -->
                <div class="flip-card-front bg-gradient-to-br from-pink-100 via-pink-150 to-purple-150 p-8 shadow-lg flex flex-col items-center justify-center text-center border border-pink-200/30">
                  <span class="text-6xl mb-6 animate-soft-pulse">{{ loveNote.emoji }}</span>
                  <h3 class="font-playfair text-2xl font-semibold text-gray-700 mb-3">{{ loveNote.title }}</h3>
                  <p class="text-sm text-gray-500 font-poppins font-light">Hover to see why ✨</p>
                </div>
                
                <!-- Back of card -->
                <div class="flip-card-back bg-white/90 backdrop-blur-sm p-8 shadow-lg flex items-center justify-center border border-pink-200/30">
                  <div class="text-center">
                    <span class="text-4xl mb-6 block animate-soft-pulse">💖</span>
                    <p class="font-poppins text-gray-700 leading-relaxed text-base font-light">{{ loveNote.message }}</p>
                  </div>
                </div>
              </div>
            </div>
          }
        </div>

        <!-- Mobile-friendly alternative for touch devices -->
        <div class="mt-12 sm:hidden">
          <p class="text-center text-sm text-gray-500 font-poppins font-light">
            💡 Tap cards to see the messages on mobile
          </p>
        </div>

        <!-- Final Message -->
        <div class="mt-20 text-center">
          <div class="glass-effect rounded-3xl p-12 sm:p-16 shadow-xl border border-white/30 max-w-4xl mx-auto">
            <div class="mb-8">
              <span class="text-5xl animate-soft-pulse">💖</span>
            </div>
            
            <h2 class="font-dancing text-3xl sm:text-4xl lg:text-5xl text-romantic mb-8">
              Happy 1st Monthsary, my love!
            </h2>
            
            <p class="font-poppins text-lg sm:text-xl text-gray-700 leading-relaxed max-w-3xl mx-auto font-light">
              Every day with you feels like a new adventure, and I can't wait to see what 
              amazing memories we'll create together. to many more months, years, 
              and a lifetime of love, laughter, and beautiful moments my lovelove tangi.
            </p>
          </div>
        </div>
      </div>
    </section>
  `,
  styles: []
})
export class LoveNotesComponent {
  loveNotes = [
    {
      id: 1,
      emoji: '😍',
      title: 'Your Smile',
      message: 'Your smile lights up my entire world and makes even the darkest days feel bright and beautiful.'
    },
    {
      id: 2,
      emoji: '💖',
      title: 'Your  Genguiness',
      message: 'The way you care for me and show compassion makes me fall in love with you more every day.'
    },
    {
      id: 3,
      emoji: '🌟',
      title: 'Your Intelligence',
      message: 'Your brilliant mind and the way you see the world never fails to amaze and inspire me.'
    },
    {
      id: 4,
      emoji: '🎵',
      title: 'Your Voice',
      message: 'Your Voice is my favorite melody, and I would do anything just to hear it every day.'
    },
    {
      id: 5,
      emoji: '🤗',
      title: 'Your Hugs',
      message: 'In your arms, I have found my home, my peace, and my greatest comfort in this world.'
    },
    {
      id: 6,
      emoji: '✨',
      title: 'Your Dreams',
      message: 'The passion you have for your dreams and goals motivates me to be the best version of myself.'
    }
  ];
}
